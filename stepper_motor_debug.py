# 二维步进电机调试控制系统 - K230平台
# 通过串口接收控制命令，驱动X/Y轴步进电机
# 每一步都有详细的调试信息输出

import time
import os
from machine import FPIOA, Pin, UART
from time import ticks_ms

# 全局变量
fpioa = None
uart2 = None
step_x1 = None
step_x2 = None
dir_x1 = None
dir_x2 = None
step_y1 = None
step_y2 = None
dir_y1 = None
dir_y2 = None

# 电机状态记录
x_position = 0  # X轴当前位置（步数）
y_position = 0  # Y轴当前位置（步数）
total_x_steps = 0  # X轴总步数
total_y_steps = 0  # Y轴总步数
step_count = 0

print("=" * 50)
print("二维步进电机调试控制系统启动")
print("=" * 50)
        
    def init_uart(self, uart_id=1, baudrate=115200):
        """初始化串口通信"""
        try:
            self.uart = UART(uart_id, baudrate=baudrate, bits=8, parity=None, stop=1)
            print(f"✓ 串口初始化成功: UART{uart_id}, 波特率: {baudrate}")
            return True
        except Exception as e:
            print(f"✗ 串口初始化失败: {e}")
            return False
    
    def init_gpio(self):
        """初始化GPIO引脚"""
        print("\n开始初始化GPIO引脚...")
        
        # X轴步进电机 - GPIO15, 16, 17, 19
        try:
            print("配置X轴步进电机引脚...")
            self.fpioa.set_function(15, FPIOA.GPIO15)
            self.fpioa.set_function(16, FPIOA.GPIO16)
            self.fpioa.set_function(17, FPIOA.GPIO17)
            self.fpioa.set_function(19, FPIOA.GPIO19)

            self.step_x1 = Pin(15, Pin.OUT)
            self.dir_x1 = Pin(16, Pin.OUT)
            self.step_x2 = Pin(17, Pin.OUT)
            self.dir_x2 = Pin(19, Pin.OUT)
            
            # 初始化为低电平
            self.step_x1.value(0)
            self.dir_x1.value(0)
            self.step_x2.value(0)
            self.dir_x2.value(0)
            
            print("✓ X轴步进电机GPIO配置成功")
            print(f"  - STEP_X1: GPIO15, DIR_X1: GPIO16")
            print(f"  - STEP_X2: GPIO17, DIR_X2: GPIO19")
            
        except Exception as e:
            print(f"✗ X轴步进电机配置失败: {e}")
            return False

        # Y轴步进电机 - GPIO27, 14, 61, 40
        try:
            print("配置Y轴步进电机引脚...")
            self.fpioa.set_function(27, FPIOA.GPIO27)
            self.fpioa.set_function(14, FPIOA.GPIO14)
            self.fpioa.set_function(61, FPIOA.GPIO61)
            self.fpioa.set_function(40, FPIOA.GPIO40)

            self.step_y1 = Pin(27, Pin.OUT)
            self.dir_y1 = Pin(14, Pin.OUT)
            self.step_y2 = Pin(61, Pin.OUT)
            self.dir_y2 = Pin(40, Pin.OUT)
            
            # 初始化为低电平
            self.step_y1.value(0)
            self.dir_y1.value(0)
            self.step_y2.value(0)
            self.dir_y2.value(0)
            
            print("✓ Y轴步进电机GPIO配置成功")
            print(f"  - STEP_Y1: GPIO27, DIR_Y1: GPIO14")
            print(f"  - STEP_Y2: GPIO61, DIR_Y2: GPIO40")
            
        except Exception as e:
            print(f"✗ Y轴步进电机配置失败: {e}")
            return False
            
        print("✓ 所有GPIO引脚初始化完成\n")
        return True
    
    def debug_print(self, message):
        """调试信息输出"""
        if self.debug_enabled:
            timestamp = ticks_ms()
            print(f"[{timestamp}] {message}")
            
    def send_uart_response(self, response):
        """通过串口发送响应"""
        if self.uart:
            try:
                response_str = json.dumps(response) + "\n"
                self.uart.write(response_str.encode())
                self.debug_print(f"串口响应: {response}")
            except Exception as e:
                print(f"串口发送失败: {e}")
    
    def control_stepper_x(self, direction, steps, speed=1):
        """控制X轴步进电机"""
        if steps <= 0 or not all([self.step_x1, self.step_x2, self.dir_x1, self.dir_x2]):
            self.debug_print("X轴控制失败: 参数无效或GPIO未初始化")
            return False
            
        self.debug_print(f"开始X轴控制: 方向={direction}, 步数={steps}, 速度={speed}")
        
        # 设置方向
        dir_value_1 = 1 if direction > 0 else 0
        dir_value_2 = 0 if direction > 0 else 1
        
        self.dir_x1.value(dir_value_1)
        self.dir_x2.value(dir_value_2)
        
        self.debug_print(f"X轴方向设置: DIR_X1={dir_value_1}, DIR_X2={dir_value_2}")
        
        # 计算延时
        if steps > 20:
            delay = max(1, 3 - speed)  # 快速移动
        elif steps > 5:
            delay = max(1, 3 - speed)  # 中速移动
        else:
            delay = 2  # 精确移动
            
        self.debug_print(f"X轴步进延时: {delay}ms")
        
        # 发送步进脉冲
        start_time = ticks_ms()
        for i in range(abs(steps)):
            # 上升沿
            self.step_x1.value(1)
            self.step_x2.value(1)
            time.sleep_ms(delay)
            
            # 下降沿
            self.step_x1.value(0)
            self.step_x2.value(0)
            time.sleep_ms(delay)
            
            self.step_count += 1
            
            # 每10步输出一次进度
            if (i + 1) % 10 == 0 or i == abs(steps) - 1:
                progress = ((i + 1) / abs(steps)) * 100
                self.debug_print(f"X轴步进进度: {i+1}/{abs(steps)} ({progress:.1f}%)")
        
        # 更新位置
        self.x_position += steps * direction
        self.total_x_steps += abs(steps)
        
        end_time = ticks_ms()
        duration = end_time - start_time
        
        self.debug_print(f"X轴控制完成: 用时{duration}ms, 当前位置={self.x_position}")
        
        return True
    
    def control_stepper_y(self, direction, steps, speed=1):
        """控制Y轴步进电机"""
        if steps <= 0 or not all([self.step_y1, self.step_y2, self.dir_y1, self.dir_y2]):
            self.debug_print("Y轴控制失败: 参数无效或GPIO未初始化")
            return False
            
        self.debug_print(f"开始Y轴控制: 方向={direction}, 步数={steps}, 速度={speed}")
        
        # 设置方向
        dir_value_1 = 1 if direction > 0 else 0
        dir_value_2 = 0 if direction > 0 else 1
        
        self.dir_y1.value(dir_value_1)
        self.dir_y2.value(dir_value_2)
        
        self.debug_print(f"Y轴方向设置: DIR_Y1={dir_value_1}, DIR_Y2={dir_value_2}")
        
        # 计算延时
        if steps > 20:
            delay = max(1, 3 - speed)  # 快速移动
        elif steps > 5:
            delay = max(1, 3 - speed)  # 中速移动
        else:
            delay = 2  # 精确移动
            
        self.debug_print(f"Y轴步进延时: {delay}ms")
        
        # 发送步进脉冲
        start_time = ticks_ms()
        for i in range(abs(steps)):
            # 上升沿
            self.step_y1.value(1)
            self.step_y2.value(1)
            time.sleep_ms(delay)
            
            # 下降沿
            self.step_y1.value(0)
            self.step_y2.value(0)
            time.sleep_ms(delay)
            
            self.step_count += 1
            
            # 每10步输出一次进度
            if (i + 1) % 10 == 0 or i == abs(steps) - 1:
                progress = ((i + 1) / abs(steps)) * 100
                self.debug_print(f"Y轴步进进度: {i+1}/{abs(steps)} ({progress:.1f}%)")
        
        # 更新位置
        self.y_position += steps * direction
        self.total_y_steps += abs(steps)
        
        end_time = ticks_ms()
        duration = end_time - start_time
        
        self.debug_print(f"Y轴控制完成: 用时{duration}ms, 当前位置={self.y_position}")
        
        return True

    def move_to_position(self, x_target, y_target, speed=2):
        """移动到指定位置（相对于当前位置）"""
        self.debug_print(f"移动到目标位置: X={x_target}, Y={y_target}")

        # 计算需要移动的步数
        x_steps = x_target - self.x_position
        y_steps = y_target - self.y_position

        self.debug_print(f"需要移动: X轴{x_steps}步, Y轴{y_steps}步")

        success = True

        # 先移动X轴
        if x_steps != 0:
            direction_x = 1 if x_steps > 0 else -1
            if not self.control_stepper_x(direction_x, abs(x_steps), speed):
                success = False

        # 再移动Y轴
        if y_steps != 0:
            direction_y = 1 if y_steps > 0 else -1
            if not self.control_stepper_y(direction_y, abs(y_steps), speed):
                success = False

        return success

    def home_position(self):
        """回到原点位置"""
        self.debug_print("开始回到原点...")

        # 移动到(0,0)位置
        success = self.move_to_position(0, 0)

        if success:
            self.debug_print("已回到原点位置")
        else:
            self.debug_print("回到原点失败")

        return success

    def get_status(self):
        """获取当前状态"""
        status = {
            "x_position": self.x_position,
            "y_position": self.y_position,
            "total_x_steps": self.total_x_steps,
            "total_y_steps": self.total_y_steps,
            "total_steps": self.step_count,
            "gpio_initialized": all([self.step_x1, self.step_x2, self.step_y1, self.step_y2]),
            "uart_initialized": self.uart is not None
        }
        return status

    def process_command(self, command_str):
        """处理串口命令"""
        try:
            command = json.loads(command_str.strip())
            self.debug_print(f"收到命令: {command}")

            cmd_type = command.get("cmd", "")
            response = {"cmd": cmd_type, "success": False, "message": ""}

            if cmd_type == "move_x":
                # X轴移动命令: {"cmd": "move_x", "direction": 1, "steps": 10, "speed": 2}
                direction = command.get("direction", 1)
                steps = command.get("steps", 1)
                speed = command.get("speed", 2)

                success = self.control_stepper_x(direction, steps, speed)
                response["success"] = success
                response["message"] = f"X轴移动{'成功' if success else '失败'}"
                response["position"] = self.x_position

            elif cmd_type == "move_y":
                # Y轴移动命令: {"cmd": "move_y", "direction": 1, "steps": 10, "speed": 2}
                direction = command.get("direction", 1)
                steps = command.get("steps", 1)
                speed = command.get("speed", 2)

                success = self.control_stepper_y(direction, steps, speed)
                response["success"] = success
                response["message"] = f"Y轴移动{'成功' if success else '失败'}"
                response["position"] = self.y_position

            elif cmd_type == "move_xy":
                # 双轴移动命令: {"cmd": "move_xy", "x_dir": 1, "x_steps": 10, "y_dir": -1, "y_steps": 5, "speed": 2}
                x_dir = command.get("x_dir", 0)
                x_steps = command.get("x_steps", 0)
                y_dir = command.get("y_dir", 0)
                y_steps = command.get("y_steps", 0)
                speed = command.get("speed", 2)

                success_x = True
                success_y = True

                if x_steps > 0:
                    success_x = self.control_stepper_x(x_dir, x_steps, speed)
                if y_steps > 0:
                    success_y = self.control_stepper_y(y_dir, y_steps, speed)

                success = success_x and success_y
                response["success"] = success
                response["message"] = f"双轴移动{'成功' if success else '失败'}"
                response["x_position"] = self.x_position
                response["y_position"] = self.y_position

            elif cmd_type == "move_to":
                # 移动到指定位置: {"cmd": "move_to", "x": 100, "y": 50, "speed": 2}
                x_target = command.get("x", self.x_position)
                y_target = command.get("y", self.y_position)
                speed = command.get("speed", 2)

                success = self.move_to_position(x_target, y_target, speed)
                response["success"] = success
                response["message"] = f"移动到位置({'成功' if success else '失败'})"
                response["x_position"] = self.x_position
                response["y_position"] = self.y_position

            elif cmd_type == "home":
                # 回到原点: {"cmd": "home"}
                success = self.home_position()
                response["success"] = success
                response["message"] = f"回到原点{'成功' if success else '失败'}"
                response["x_position"] = self.x_position
                response["y_position"] = self.y_position

            elif cmd_type == "status":
                # 获取状态: {"cmd": "status"}
                status = self.get_status()
                response["success"] = True
                response["message"] = "状态获取成功"
                response.update(status)

            elif cmd_type == "debug":
                # 调试开关: {"cmd": "debug", "enable": true}
                enable = command.get("enable", True)
                self.debug_enabled = enable
                response["success"] = True
                response["message"] = f"调试模式{'开启' if enable else '关闭'}"

            elif cmd_type == "reset":
                # 重置位置计数: {"cmd": "reset"}
                self.x_position = 0
                self.y_position = 0
                self.total_x_steps = 0
                self.total_y_steps = 0
                self.step_count = 0
                response["success"] = True
                response["message"] = "位置计数已重置"

            else:
                response["message"] = f"未知命令: {cmd_type}"

            self.send_uart_response(response)

        except json.JSONDecodeError as e:
            self.debug_print(f"JSON解析错误: {e}")
            error_response = {
                "cmd": "error",
                "success": False,
                "message": f"JSON格式错误: {e}"
            }
            self.send_uart_response(error_response)

        except Exception as e:
            self.debug_print(f"命令处理错误: {e}")
            error_response = {
                "cmd": "error",
                "success": False,
                "message": f"处理错误: {e}"
            }
            self.send_uart_response(error_response)

    def run(self):
        """主运行循环"""
        print("\n开始主运行循环...")
        print("等待串口命令...")
        print("\n支持的命令格式:")
        print('{"cmd": "move_x", "direction": 1, "steps": 10, "speed": 2}')
        print('{"cmd": "move_y", "direction": -1, "steps": 5, "speed": 1}')
        print('{"cmd": "move_xy", "x_dir": 1, "x_steps": 10, "y_dir": -1, "y_steps": 5}')
        print('{"cmd": "move_to", "x": 100, "y": 50, "speed": 2}')
        print('{"cmd": "home"}')
        print('{"cmd": "status"}')
        print('{"cmd": "debug", "enable": true}')
        print('{"cmd": "reset"}')
        print("\n" + "=" * 50)

        buffer = ""

        try:
            while True:
                # 检查串口数据
                if self.uart and self.uart.any():
                    data = self.uart.read()
                    if data:
                        buffer += data.decode('utf-8', errors='ignore')

                        # 处理完整的命令行（以换行符分隔）
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            if line.strip():
                                self.process_command(line.strip())

                # 短暂延时
                time.sleep_ms(100)

        except KeyboardInterrupt:
            print("\n用户停止程序")
        except Exception as e:
            print(f"运行错误: {e}")
        finally:
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        print("\n开始清理资源...")

        # 停止所有GPIO
        if self.step_x1:
            self.step_x1.value(0)
            self.step_x2.value(0)
        if self.step_y1:
            self.step_y1.value(0)
            self.step_y2.value(0)

        print("✓ GPIO引脚已清理")
        print("✓ 步进电机调试系统已安全关闭")

# 主程序入口
if __name__ == "__main__":
    controller = StepperMotorController()

    # 初始化串口
    if not controller.init_uart(uart_id=1, baudrate=115200):
        print("串口初始化失败，程序退出")
        exit(1)

    # 初始化GPIO
    if not controller.init_gpio():
        print("GPIO初始化失败，程序退出")
        exit(1)

    print("✓ 步进电机调试系统初始化完成")
    print("✓ 准备接收串口命令...")

    # 发送初始化完成信号
    init_response = {
        "cmd": "init",
        "success": True,
        "message": "步进电机调试系统初始化完成",
        "version": "1.0"
    }
    controller.send_uart_response(init_response)

    # 开始运行
    controller.run()
